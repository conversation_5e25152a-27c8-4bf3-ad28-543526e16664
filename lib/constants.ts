// ======================== config keys ========================
// Analytics
export const MIXPANEL_TOKEN = "";
export const CLARITY_TOKEN = "qb74yecqmp";
export const GA_TOKEN = "8KTLJXCEVQ";
export const UMAMI_TOKEN = "a5fa3177-b292-4070-82c2-6a0fd74b0149";

// ======================== utils keys ========================
export const WEB_HOST = "unoscribe.com";
export const WEB_URL = process.env.NODE_ENV === "production" ? `https://${WEB_HOST}` : "http://localhost:3000";
export const WEBNAME = "Unoscribe";
export const EMAIL_CONTACT = "<EMAIL>";
export const CALLBACK_URL_TRANSCRIBE_WEBHOOK = process.env.NODE_ENV === "production" ? "https://unoscribe.com" : "https://dev-next.xav.im";

// Auth
export const ROUTE_PATH_SIGN_IN = "/";
export const ROUTE_PATH_SIGN_IN_AFTER = "/";

// OSS
export const CLOUDFLARE_R2_BUCKET_NAME = "unoscribe";
export const OSS_URL = "https://static.unoscribe.com";

// Cookie

// Duration
export const DURATION_1_HOUR = 60 * 60;
export const DURATION_6_HOUR = 6 * DURATION_1_HOUR;
export const DURATION_1_DAY = 24 * DURATION_1_HOUR;
export const DURATION_1_WEEK = 7 * DURATION_1_DAY;
export const DURATION_1_MONTH = 30 * DURATION_1_DAY;
export const DURATION_6_MONTH = 180 * DURATION_1_DAY;

// File Upload limit
export const FILE_SIZE_LIMIT_FREE = 1024 * 1024 * 500; // 500MB
export const FILE_SIZE_LIMIT_MAX = 1024 * 1024 * 2048; // 2GB
export const FILE_DURATION_LIMIT_FREE = 60 * 30; // 30 minutes limit for free
export const FILE_DURATION_LIMIT_STATER = 60 * 60 * 2; // 2 hours limit for stater
export const FILE_DURATION_LIMIT_2_HOUR = 60 * 60 * 2; // 2 hours
