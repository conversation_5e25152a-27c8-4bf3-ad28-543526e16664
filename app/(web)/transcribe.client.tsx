"use client";

import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { SparklesIcon, CircleHelp, Copy, LucideIcon, File, Link, UploadIcon, X, ChevronDown, AlignLeft, ChevronLeft } from "lucide-react";
import { SubmitButton } from "@/components/ui/submit-button";
import { ofetch } from "ofetch";
import { AuthError, FreeLimitError, handleError, IgnoreError } from "@/@types/error";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from "@/components/ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { getLanguageName, getLanguages } from "@/lib/languages";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useCopyToClipboard } from "usehooks-ts";
import { Hint } from "@/components/ui/custom/hint";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useSession } from "@/lib/auth-client";
import { useSignInBoxOpenStore } from "@/store/useSignInBoxOpenStore";
import { getUploadFileUrl, handleFileUploadWithProgress, uploadFile } from "@/lib/file/upload-file";
import { useUserStore } from "@/store/useUserStore";
import { usePlanBoxOpenStore } from "@/store/usePlanBoxOpenStore";
import { MembershipID } from "@/@types/membership-type";
import { FILE_DURATION_LIMIT_FREE, FILE_DURATION_LIMIT_STATER } from "@/lib/constants";
import { useRouter } from "nextjs-toploader/app";
import { Dropzone, DropzoneContent, DropzoneEmptyState } from "@/components/ui/kibo-ui/dropzone";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { formatTime } from "@/lib/utils-date";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { IconMusicFile } from "@/components/icons";
import { useCookies } from "next-client-cookies";
import { Progress } from "@/components/ui/custom/progress";
import { useSubmitTimer } from "@/hooks/use-submit-timer";
import { useSubmitTimer1 } from "@/hooks/use-submit-timer-1";
import { MediaProvider } from "media-chrome/react/media-store";
import MediaPlayer from "./media-player";
import ProjectTranscriptClient from "./project-transcript.client";

const languages = getLanguages.sort((a, b) => a.name.localeCompare(b.name));

type FileSourceType = {
	id: string;
	name: string;
	icon: LucideIcon;
};
const fileTypes: FileSourceType[] = [
	{
		id: "file",
		name: "File",
		icon: File,
	},
	{
		id: "url",
		name: "URL",
		icon: Link,
	},
];

export default function TranscribeClient({ initialFileType = "file" }: { initialFileType?: string }) {
	const COOKIE_KEY_RECENT_LANGUAGE = "recent_language";
	const router = useRouter();
	const cookies = useCookies();
	const { data: session } = useSession();
	const { user } = useUserStore();
	const { setSignInBoxOpen } = useSignInBoxOpenStore();
	const { setPlanBoxOpen } = usePlanBoxOpenStore();
	const [copiedText, copy] = useCopyToClipboard();
	const onCopyFromSentences = (sentences: SentenceData[]) => {
		const content = sentences.map((sentence) => sentence.text).join(" ");
		copy(content)
			.then(() => {
				toast.success("Transcript copied!");
			})
			.catch((error: any) => {
				toast.error("Failed to copy!", error);
			});
	};

	// ======== Upload & Transcribe Configuration ========
	const [languageCode, setLanguageCode] = useState<string>("en");
	const [languageOpen, setLanguageOpen] = useState<boolean>(false);
	const [recentLanguageCodes, setRecentLanguageCodes] = useState<string[]>([]);
	const handleInitialRecentLanguage = () => {
		const recentLanguageCodesString = cookies.get(COOKIE_KEY_RECENT_LANGUAGE);
		console.log("recentLanguageCodesString:", recentLanguageCodesString);
		if (!recentLanguageCodesString) return;
		const recentLanguageCodes = recentLanguageCodesString.split(",");
		setRecentLanguageCodes(recentLanguageCodes);
	};
	const handleAddNewRecentLanguage = (languageCode: string) => {
		if (!languageCode) return;
		if (recentLanguageCodes.includes(languageCode)) return;
		const newRecentLanguageCode = [languageCode, ...recentLanguageCodes];
		setRecentLanguageCodes(newRecentLanguageCode);
		cookies.set(COOKIE_KEY_RECENT_LANGUAGE, newRecentLanguageCode.join(","), {
			path: "/",
		});
	};
	useEffect(() => {
		handleInitialRecentLanguage();
	}, []);

	const [fileSourceType, setFileSourceType] = useState<FileSourceType>(fileTypes.find((type) => type.id === initialFileType) || fileTypes[0]);
	const [configSpeaker, setConfigSpeaker] = useState<boolean>(false);
	const [selectFile, setSelectFile] = useState<{
		fileName: string;
		fileDuration: number;
		fileSize: number;
	} | null>(null);
	const [selectFileUrl, setSelectFileUrl] = useState<string | null>(null);

	const [uploadingFile, setUploadingFile] = useState<boolean>(false);
	const [uploadingFileProgress, setUploadingFileProgress] = useState<number>(0);
	const handleLocalFileDrop = async (files: File[]) => {
		if (uploadingFile) return;
		if (!session?.user) {
			setSignInBoxOpen(true);
			return;
		}
		if (!files || files.length === 0) return;
		const file = files[0];
		// file.size > 100 * 1024 * 1024 && toast.error("File size greater than 100MB, please upload a smaller file.");

		try {
			setSelectFile(null);
			setSelectFileUrl(null);

			// 获得音频文件的时长数据
			const duration = await new Promise<number>((resolve, reject) => {
				const audio = new Audio();
				const url = URL.createObjectURL(file);
				audio.src = url;

				audio.onloadedmetadata = () => {
					const duration = Number(audio.duration.toFixed(2));
					URL.revokeObjectURL(url); // Clean up the URL
					resolve(duration);
				};

				audio.onerror = (error) => {
					URL.revokeObjectURL(url);
					reject(new Error("Error loading audio file"));
				};
			});

			// Check duration limits
			if ((!user || user.membershipId === MembershipID.Free) && duration > FILE_DURATION_LIMIT_FREE) {
				toast.error("File duration greater than 30 minutes, upgrade to upload file up to 5 hours.", {
					action: {
						label: "Upgrade",
						onClick: () => {
							setPlanBoxOpen(true);
						},
					},
				});
				return;
			}
			if (user && user.membershipId === MembershipID.Starter && duration > FILE_DURATION_LIMIT_STATER) {
				toast.error("File duration greater than 2 hours, upgrade to upload file up to 5 hours.", {
					action: {
						label: "Upgrade",
						onClick: () => {
							router.push("/user/my-subscriptions");
						},
					},
				});
				return;
			}

			// Proceed with file upload
			setUploadingFile(true);
			setUploadingFileProgress(0);

			// upload audio or video to backend (/api/v1/file-handle), then get auido file url
			const fileUrl = "";

			// Set file info
			setSelectFile({
				fileName: file.name,
				fileDuration: duration,
				fileSize: file.size,
			});
			setSelectFileUrl(fileUrl);
		} catch (error: any) {
			console.error("Failed to upload file.:", error.message);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			toast.error(`Failed to upload file: ${error.message}`);
		} finally {
			setUploadingFile(false);
		}
	};

	const [submitting, setSubmitting] = useState(false);
	const [sentences, setSentences] = useState<SentenceData[] | null>(null);
	const { seconds } = useSubmitTimer1(submitting);
	const handleFileTranscribe = async () => {
		void handleAddNewRecentLanguage(languageCode);
		if (submitting) return;
		if (!session?.user) {
			setSignInBoxOpen(true);
			return;
		}

		try {
			setSubmitting(true);
			setSentences(null);
			const {
				status,
				message,
				sentences: sentencesResult,
			} = await ofetch("/api/v1/transcribe", {
				method: "POST",
				body: {
					source: fileSourceType.id,
					fileUrl: selectFileUrl,
					fileName: selectFile?.fileName,
					lang: languageCode,
					speaker: configSpeaker,
					d: selectFile?.fileDuration,
				},
			});
			handleError(status, message);
			setSentences(sentencesResult);

			toast.success("Transcription completed.");
		} catch (error: any) {
			console.error("Failed to transcribe file:", error.message);
			if (error instanceof AuthError) {
				setSignInBoxOpen(true);
				return;
			}
			if (error instanceof FreeLimitError) {
				toast.warning(error.message, {
					action: {
						label: "Get more",
						onClick: () => setPlanBoxOpen(true),
					},
				});
				return;
			}
			if (error instanceof IgnoreError) {
				return;
			}
			toast.error("Transcription failed.");
		} finally {
			setSubmitting(false);
		}
	};

	return (
		<>
			{sentences ? (
				<div className="bg-muted mx-auto mt-4 rounded-xl border p-4" id="transcript">
					<div className="flex flex-col rounded-xl bg-white">
						<MediaProvider>
							<div className="flex flex-row items-center justify-between gap-1 border-b px-4 py-2">
								<div className="text-muted-foreground flex flex-row items-center gap-1">
									<Button size="sm" className="mr-1 flex-shrink-0 cursor-pointer" onClick={() => setSentences(null)}>
										<ChevronLeft className="size-4" />
										New
									</Button>
									<AlignLeft className="size-4" />
									<p className="text-sm">Transcript</p>
								</div>
								<Hint label="Copy transcript" sideOffset={10} className="">
									<Button
										className="flex-shrink-0 cursor-pointer"
										type="button"
										size="icon"
										variant="secondary"
										onClick={() => onCopyFromSentences(sentences)}
									>
										<Copy className="h-4 w-4" />
									</Button>
								</Hint>
							</div>

							<div className={cn("flex w-full grow flex-col font-[380] text-zinc-800")}>
								<ProjectTranscriptClient sentences={sentences} />
							</div>
							{selectFileUrl && (
								<div className="px-4 py-0.5">
									<MediaPlayer fileUrl={selectFileUrl} />
								</div>
							)}
						</MediaProvider>
					</div>
				</div>
			) : (
				<div className="bg-muted mx-auto max-w-3xl rounded-xl border">
					<div className="flex w-full flex-col items-center gap-6 p-4">
						<div className="flex w-full flex-col gap-2">
							{/* <Tabs className="mx-auto" value={fileSourceType.id}>
					<TabsList className="gap-1 rounded-xl border bg-white">
						{fileTypes.map((type, index) => (
							<TabsTrigger
								key={index}
								value={type.id}
								onClick={() => setFileSourceType(type)}
								className="cursor-pointer gap-1.5 rounded-lg data-[state=active]:bg-zinc-800 data-[state=active]:text-white"
							>
								<type.icon className="size-4" />
								{type.name}
							</TabsTrigger>
						))}
					</TabsList>
				</Tabs> */}

							{fileSourceType.id === "file" && (
								<div className="rounded-lg bg-white">
									{(selectFile && selectFileUrl) || uploadingFile ? (
										<div className="bg-muted space-y-4 rounded-lg">
											{uploadingFile ? (
												<div className="flex h-[88px] flex-col items-center justify-center gap-1 rounded bg-white p-4">
													<p className="text-muted-foreground text-sm">Uploading file...</p>
													<div className="flex w-full flex-row items-center justify-center gap-1 text-xs">
														<Progress
															value={uploadingFileProgress}
															className="h-1.5 max-w-[280px] bg-zinc-200"
															indicatorClassName="bg-zinc-800"
														/>
														<span className="font-mono tabular-nums">{uploadingFileProgress}%</span>
													</div>
												</div>
											) : (
												<Table className="">
													<TableHeader className="[&_tr]:border-b-0">
														<TableRow className="bg-zinc-50 hover:bg-zinc-50">
															<TableHead className="text-xs font-medium text-zinc-500">File</TableHead>
															<TableHead className="text-xs font-medium text-zinc-500">Duration</TableHead>
															<TableHead className="text-xs font-medium text-zinc-500"></TableHead>
														</TableRow>
													</TableHeader>
													<TableBody>
														<TableRow className="bg-white hover:bg-zinc-50">
															<TableCell className="flex w-full flex-row items-center gap-1">
																<IconMusicFile className="h-5 w-5 flex-shrink-0 text-green-500" />
																<span className="font-[380] text-wrap text-zinc-800">{selectFile!.fileName}</span>
															</TableCell>
															<TableCell className="text-muted-foreground mx-auto text-xs font-[380] md:w-[100px]">
																{formatTime(selectFile!.fileDuration)}
															</TableCell>
															<TableCell className="text-muted-foreground mx-auto md:w-[20px]">
																<Button
																	size="sm"
																	variant="ghost"
																	className={cn("text-zinc-600", submitting ? "cursor-not-allowed" : "cursor-pointer")}
																	onClick={() => {
																		if (!submitting) {
																			setSelectFile(null);
																			setSelectFileUrl(null);
																		}
																	}}
																>
																	<X />
																</Button>
															</TableCell>
														</TableRow>
													</TableBody>
												</Table>
											)}

											<div className="space-y-3 rounded bg-white px-1.5 py-3">
												<div className="space-y-2">
													<p className="flex flex-row items-center">
														<span className="text-destructive mr-1 w-2 text-sm font-light">*</span>
														<span className="text-sm">Audio language</span>
													</p>
													<div className="pl-3">
														<Popover open={languageOpen} onOpenChange={setLanguageOpen}>
															<PopoverTrigger asChild>
																<Button
																	variant="outline"
																	size="default"
																	role="combobox"
																	aria-expanded={languageOpen}
																	className="w-full cursor-pointer justify-between px-3 font-[380] text-zinc-700 shadow-none sm:w-[200px]"
																>
																	{getLanguageName(languageCode) || "Language"}
																	<ChevronDown className="text-zinc-400" />
																</Button>
															</PopoverTrigger>
															<PopoverContent className="w-[220px] p-0">
																<Command>
																	<CommandInput placeholder="Search Language" />
																	<CommandEmpty>No Language found.</CommandEmpty>
																	<CommandGroup className="p-0 py-1">
																		<ScrollArea className="h-72" type="always">
																			{recentLanguageCodes && recentLanguageCodes.length > 0 && (
																				<>
																					<p className="text-muted-foreground px-2 py-1 text-xs font-[380]">
																						Recently used
																					</p>
																					{recentLanguageCodes.map((languageItem) => (
																						<CommandItem
																							key={languageItem}
																							value={getLanguageName(languageItem)}
																							onSelect={() => {
																								setLanguageCode(languageItem);
																								setLanguageOpen(false);
																							}}
																							className={cn(
																								"mx-1 my-1 cursor-pointer justify-between text-zinc-700",
																								languageCode === languageItem &&
																									"bg-accent text-accent-foreground",
																							)}
																						>
																							{getLanguageName(languageItem)}
																						</CommandItem>
																					))}
																					<p className="text-muted-foreground mt-2 px-2 py-1 text-xs font-[380]">
																						All
																					</p>
																				</>
																			)}
																			{languages.map((languageItem) => {
																				if (recentLanguageCodes.includes(languageItem.value)) {
																					return null;
																				}
																				return (
																					<CommandItem
																						key={languageItem.value}
																						value={languageItem.name}
																						onSelect={() => {
																							setLanguageCode(languageItem.value);
																							setLanguageOpen(false);
																						}}
																						className={cn(
																							"mx-1 my-1 cursor-pointer justify-between text-zinc-700",
																							languageCode === languageItem.value &&
																								"bg-accent text-accent-foreground",
																						)}
																					>
																						{languageItem.name}
																					</CommandItem>
																				);
																			})}
																		</ScrollArea>
																	</CommandGroup>
																</Command>
															</PopoverContent>
														</Popover>
													</div>
												</div>
												{/* <div className="space-y-2">
													<p className="flex flex-row items-center">
														<span className="text-destructive mr-1 w-2 text-sm font-light">*</span>
														<span className="text-sm">Configure speaker recognition</span>
													</p>
													<div className="flex flex-row items-center pl-3 text-zinc-600">
														<RadioGroup
															defaultValue="false"
															className="gap-1.5"
															value={configSpeaker ? "true" : "false"}
															onValueChange={(value) => {
																setConfigSpeaker(value === "true");
															}}
														>
															<div className="flex items-center space-x-2">
																<RadioGroupItem value="false" id="no-speaker-recognition" className="cursor-pointer" />
																<Label htmlFor="no-speaker-recognition" className="cursor-pointer text-[13px] font-[380]">
																	No speaker recognition
																</Label>
															</div>
															<div className="flex items-center space-x-2">
																<RadioGroupItem value="true" id="recognize-speakers" className="cursor-pointer" />
																<Label htmlFor="recognize-speakers" className="cursor-pointer text-[13px] font-[380]">
																	Recognize Speakers
																</Label>
															</div>
														</RadioGroup>
													</div>
												</div> */}
											</div>
										</div>
									) : (
										<div
											onClick={() => {
												if (!session) {
													setSignInBoxOpen(true);
												}
											}}
										>
											<Dropzone
												multiple={false}
												maxFiles={1}
												noClick={!session}
												onDragEnter={() => {
													if (!session) {
														setSignInBoxOpen(true);
														return;
													}
												}}
												onDrop={handleLocalFileDrop}
												accept={{ "audio/*": [], "video/*": [] }}
												onError={console.error}
												className={cn("hover:border-primary cursor-pointer border-dashed bg-white whitespace-pre-wrap hover:bg-sky-50")}
											>
												<DropzoneEmptyState>
													<>
														<div className="flex size-8 items-center justify-center rounded-md bg-neutral-200 text-neutral-700">
															<UploadIcon size={16} />
														</div>
														<p className="my-2 w-full text-sm font-normal">
															Drag & drop a file here or <span className="text-blue-500">Browser</span>
														</p>
													</>
												</DropzoneEmptyState>
												<DropzoneContent />
											</Dropzone>
										</div>
									)}
								</div>
							)}
							<div className="mt-4 flex flex-row flex-wrap items-center justify-center gap-3">
								<SubmitButton
									size="lg"
									isSubmitting={submitting}
									variant="default"
									disabled={submitting || !selectFileUrl}
									className="w-[140px] cursor-pointer rounded-full bg-blue-500 hover:bg-blue-500"
									onClick={handleFileTranscribe}
									// data-umami-event={CLICK_GEN_QUESTION}
								>
									<SparklesIcon />
									Transcribe
								</SubmitButton>
							</div>
							{submitting && (
								<div className="mt-4 flex flex-col items-center rounded-2xl bg-white px-4 py-10">
									<CircleHelp className="size-7 text-sky-500" />
									<p className="mt-5">
										Transcribing...<span className="text-muted-foreground ml-1 text-sm tabular-nums">[{seconds}s]</span>
									</p>
									<p className="text-muted-foreground mt-2 max-w-[380px] text-center text-sm">
										Please wait while we transcribe your file. This may take a few seconds, depending on its length.
									</p>
								</div>
							)}
						</div>
					</div>
				</div>
			)}
		</>
	);
}
